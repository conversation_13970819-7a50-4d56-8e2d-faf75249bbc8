import os
import pandas as pd
try:
    import xlwings as xw
except ImportError:
    xw = None

def read_sales_file(file):
    """Try openpyxl first, fall back to xlwings if the file has corrupted styles."""
    print(f"Attempting to read: {file}")
    
    # First attempt: pandas + openpyxl (fast)
    try:
        print("  Trying openpyxl...")
        df = pd.read_excel(file, skiprows=8, usecols=range(5), dtype=str, engine="openpyxl")
        print(f"  ✓ openpyxl successful: {df.shape}")
    except Exception as first_err:
        print(f"  ✗ openpyxl failed: {first_err}")
        
        # Second attempt: xlwings
        try:
            print("  Trying xlwings...")
            if xw is None:
                raise ImportError("xlwings is not installed")
            app = xw.App(visible=False, add_book=False)
            wb = app.books.open(os.path.abspath(file))
            sht = wb.sheets[0]
            data = sht.used_range.value
            wb.close()
            app.quit()

            df = pd.DataFrame(data)
            df = df.iloc[8:, :5]  # skip first 8 rows, keep first 5 columns
            df.columns = df.iloc[0]  # first row after skip becomes header
            df = df[1:]  # drop header row from data
            print(f"  ✓ xlwings successful: {df.shape}")
        except Exception as second_err:
            print(f"  ✗ xlwings failed: {second_err}")
            return None

    # Common cleaning logic
    df = df.dropna(how='all')
    
    # Ensure we have the expected column names
    if len(df.columns) >= 5:
        df.columns = ['Periods', 'Item_Code', 'Store_Code', 'Sales_Value', 'Sales_Units']
    
    # Remove any rows that might be headers or empty
    df = df[df['Periods'] != 'Periods']
    df = df.dropna(subset=['Periods'])
    
    # Extract week ending date from Periods column
    df['Week_Ending'] = pd.to_datetime(
        df['Periods'].str.extract(r'w/e\s+(\d{2}/\d{2}/\d{2})')[0], dayfirst=True
    )
    df['Week_Ending'] = df['Week_Ending'].dt.strftime('%m/%d/%y')
    
    print(f"  Final cleaned data: {df.shape}")
    return df

# Test with one file
test_file = r'Sainsbury\2023\Sainsbury - 2023 WK2.xlsx'
if os.path.exists(test_file):
    result = read_sales_file(test_file)
    if result is not None:
        print("\nFirst 5 rows of processed data:")
        print(result.head())
        print(f"\nColumns: {result.columns.tolist()}")
    else:
        print("Failed to process file")
else:
    print(f"File {test_file} does not exist")
