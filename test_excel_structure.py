import pandas as pd
import os

# Test script to examine Excel file structure
test_file = r'Sainsbury\2023\Sainsbury - 2023 WK2.xlsx'  # Using WK2 instead of WK1 to avoid the problematic file

if os.path.exists(test_file):
    try:
        # Read first 15 rows to see the structure
        print("Reading first 15 rows to understand structure:")
        df_test = pd.read_excel(test_file, header=None, nrows=15, engine="openpyxl")
        print(df_test)
        print("\n" + "="*50 + "\n")
        
        # Try reading with skiprows=8 (data starts from row 9)
        print("Reading with skiprows=8 (assuming data starts from row 9):")
        df_data = pd.read_excel(test_file, skiprows=8, usecols=range(5), dtype=str, engine="openpyxl")
        print("Shape:", df_data.shape)
        print("Columns:", df_data.columns.tolist())
        print("First 5 rows:")
        print(df_data.head())
        
    except Exception as e:
        print(f"Error reading {test_file}: {e}")
        
        # Try with xlwings as fallback
        try:
            import xlwings as xw
            print("\nTrying with xlwings...")
            app = xw.App(visible=False, add_book=False)
            wb = app.books.open(os.path.abspath(test_file))
            sht = wb.sheets[0]
            
            # Read first 15 rows to see structure
            data = sht.range('A1:E15').value
            wb.close()
            app.quit()
            
            print("First 15 rows using xlwings:")
            for i, row in enumerate(data, 1):
                print(f"Row {i}: {row}")
                
        except Exception as xw_error:
            print(f"xlwings also failed: {xw_error}")
else:
    print(f"File {test_file} does not exist")
