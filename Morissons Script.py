import os
import pandas as pd
import glob
try:
    import xlwings as xw  # Used as a fallback engine when openpyxl fails
except ImportError:
    xw = None  # Script will still run; but fallback will be unavailable

# Helper function to try multiple engines when reading the Excel files
def read_sales_file(file):
    """Try openpyxl first, fall back to xlwings if the file has corrupted styles."""
    # First attempt: pandas + openpyxl (fast)
    try:
        df = pd.read_excel(file, header=8, usecols=range(5), dtype=str, engine="openpyxl")
    except Exception as first_err:
        # Second attempt: xlwings (uses the local Excel installation and ignores styles)
        try:
            if xw is None:
                raise ImportError("xlwings is not installed - pip install xlwings to enable fallback")
            app = xw.App(visible=False, add_book=False)
            wb = app.books.open(os.path.abspath(file))
            sht = wb.sheets[0]
            data = sht.used_range.value  # list of lists
            wb.close()
            app.quit()

            # Convert to DataFrame while replicating skiprows and usecols behaviour
            df = pd.DataFrame(data)
            df = df.iloc[8:, :5]  # skip first 8 rows, keep first 5 columns
            df.columns = df.iloc[0]  # first row after skip becomes header
            df = df[1:]  # drop header row from data
        except Exception as second_err:
            print(f"Both engines failed for {file}: {second_err} (openpyxl error was: {first_err})")
            return None

    # Common cleaning logic (shared with the original script)
    df = df.dropna(how='all')
    df = df[df.iloc[:, 0] != 'Periods']
    df['Week_Ending'] = pd.to_datetime(
        df['Periods'].str.extract(r'w/e\s+(\d{2}/\d{2}/\d{2})')[0], dayfirst=True
    )
    df['Week_Ending'] = df['Week_Ending'].dt.strftime('%m/%d/%y')
    return df


# Get folder path from user
folder_path = r'Sainsbury'
year=[2023,2024,2025]

for i in year:
    file_pattern = folder_path+f"\\{i}"+"\\*.xlsx"
    file_list = sorted(glob.glob(file_pattern))
    all_data = []
    for file in file_list:
        df = read_sales_file(file)
        if df is not None:
            all_data.append(df)
            print(f"Processed: {os.path.basename(file)}")
        else:
            print(f"Could not process {file}")

if all_data:
    combined_df = pd.concat(all_data, ignore_index=True)
    # Rename columns for readability (adjusted to 5 columns)
    combined_df.columns = [
        'Periods',        # e.g., "1 w/e 06/01/24"
        'Item_Code',      # e.g., "2000000156844"
        'Store_Code',     # e.g., "4002"
        'Sales_Units',    # e.g., "0"
        'Sales_Value',    # e.g., "0"
        'Week_Ending'     # derived from the 'Periods' column
    ]
    # Save as CSV in the same folder
    output_csv = os.path.join("Sainsbury.csv")
    combined_df.to_csv(output_csv, index=False)
    print(f"\nAll done! Master CSV saved as: {output_csv}")
else:
    print("No data found to combine!")